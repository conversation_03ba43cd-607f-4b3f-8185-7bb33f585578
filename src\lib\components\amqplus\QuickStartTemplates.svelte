<script>
	import { <PERSON><PERSON> } from '$lib/components/ui/button';
	import { <PERSON>, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';

	let selectedTemplate = $state(null);

	const templates = [
		{
			name: 'Competitive Tournament',
			description: 'Balanced settings for competitive play',
			difficulty: 'Advanced',
			nodes: 8
		},
		{
			name: 'Casual Fun',
			description: 'Relaxed settings for casual gaming',
			difficulty: 'Beginner',
			nodes: 4
		},
		{
			name: 'Speed Challenge',
			description: 'Fast-paced rounds with quick answers',
			difficulty: 'Intermediate',
			nodes: 6
		}
	];

	function loadTemplate(template) {
		selectedTemplate = template;
		console.log('Loading template:', template);
		// In a real implementation, this would route to the editor with the template loaded
	}
</script>

<section class="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
	<!-- Templates Section -->
	<Card class="border-amq-light rounded-xl border bg-white/95 shadow-lg backdrop-blur-sm">
		<CardHeader class="pb-4 text-center">
			<CardTitle class="!text-4xl font-bold text-gray-800 sm:text-3xl">
				Quick Start <span class="amq-gradient-text">Templates</span>
			</CardTitle>
			<p class="mt-2 text-gray-600 !text-xl">
				Begin with pre-configured templates and customize to your needs
			</p>
		</CardHeader>
		<CardContent>
			<div class="grid gap-6 md:grid-cols-3">
				{#each templates as template}
					<div
						class="group hover:border-amq-primary/30 relative rounded-lg border border-gray-200 bg-white p-6 transition-all duration-200 hover:shadow-md"
					>
						<div class="mb-4 flex items-start justify-between">
							<h3 class="text-lg font-semibold text-gray-800">{template.name}</h3>
							<Badge
								variant="outline"
								class="text-xs {template.difficulty === 'Beginner'
									? 'border-green-200 bg-green-50 text-green-700'
									: template.difficulty === 'Intermediate'
										? 'border-yellow-200 bg-yellow-50 text-yellow-700'
										: 'border-red-200 bg-red-50 text-red-700'}"
							>
								{template.difficulty}
							</Badge>
						</div>

						<p class="mb-4 text-sm text-gray-600">{template.description}</p>

						<div class="mb-4 flex items-center justify-between">
							<span class="text-xs text-gray-500">{template.nodes} nodes configured</span>
							<div class="flex space-x-1">
								{#each Array(template.nodes) as _, i}
									<div class="bg-amq-primary/60 h-2 w-2 rounded-full"></div>
								{/each}
							</div>
						</div>

						<Button
							on:click={() => loadTemplate(template)}
							variant="outline"
							class="border-amq-primary text-amq-primary hover:bg-amq-primary w-full cursor-pointer transition-colors duration-200 hover:text-white"
						>
							Use Template
						</Button>
					</div>
				{/each}
			</div>
		</CardContent>
	</Card>
</section>

<style>
	@import '$lib/styles/amqplus.css';
</style>
