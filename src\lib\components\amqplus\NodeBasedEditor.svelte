<script>
	import { But<PERSON> } from '$lib/components/ui/button';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import BorderBeam from '$lib/components/magic-ui/BorderBeam.svelte';

	// Sample node types for the editor
	const nodeTypes = [
		{
			name: 'Song Settings',
			description: 'Configure song selection criteria',
			icon: '🎵',
			color: 'bg-blue-500'
		},
		{
			name: 'Player Rules',
			description: 'Set player restrictions and permissions',
			icon: '👥',
			color: 'bg-green-500'
		},
		{
			name: 'Scoring System',
			description: 'Customize point calculation methods',
			icon: '🏆',
			color: 'bg-yellow-500'
		},
		{
			name: 'Game Flow',
			description: 'Control round timing and progression',
			icon: '⚡',
			color: 'bg-purple-500'
		},
		{
			name: 'Filters',
			description: 'Apply advanced filtering logic',
			icon: '🔍',
			color: 'bg-red-500'
		},
		{
			name: 'Output',
			description: 'Generate final lobby configuration',
			icon: '📤',
			color: 'bg-indigo-500'
		}
	];

	function startEditor() {
		// Navigate to the editor page
		window.location.href = '/editor';
	}
</script>

<section class="mx-auto w-full max-w-7xl px-4 sm:px-6 lg:px-8">
	<!-- Node-Based Editor Section -->
	<Card class="border-amq-light relative rounded-xl border bg-white/95 shadow-lg backdrop-blur-sm">
		<BorderBeam />
		<CardHeader class="pb-4 text-center">
			<CardTitle class="!text-4xl font-bold text-gray-800 sm:text-3xl">
				Node-Based <span class="amq-gradient-text">Editor</span>
			</CardTitle>
			<p class="mt-2 !text-xl text-gray-600">
				Drag, connect, and configure nodes to create your perfect AMQ lobby setup
			</p>
		</CardHeader>
		<CardContent>
			<div class="mb-8 grid grid-cols-2 gap-4 sm:grid-cols-3 lg:grid-cols-6">
				{#each nodeTypes as nodeType}
					<div class="group relative">
						<div
							class="hover:border-amq-primary/30 rounded-lg border border-gray-200 bg-white p-4 text-center transition-all duration-200 hover:shadow-md"
						>
							<div class="mb-2 text-2xl">{nodeType.icon}</div>
							<div class="mb-1 text-sm font-medium text-gray-800">{nodeType.name}</div>
							<div class="text-xs text-gray-500">{nodeType.description}</div>
						</div>
						<div
							class="absolute -top-1 -right-1 h-3 w-3 {nodeType.color} rounded-full opacity-80"
						></div>
					</div>
				{/each}
			</div>

			<div class="text-center">
				<a href="/editor">
					<Button
						onclick={startEditor}
						class="bg-amq-primary hover:bg-amq-dark cursor-pointer rounded-lg px-8 py-3 text-lg font-semibold text-white shadow-lg transition-colors duration-200 hover:shadow-xl"
					>
						Start Building
					</Button>
				</a>
			</div>
		</CardContent>
	</Card>
</section>

<style>
	@import '$lib/styles/amqplus.css';
</style>
