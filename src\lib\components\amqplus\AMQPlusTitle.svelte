<script>
	import { fade } from 'svelte/transition';
	import GradualSpacing from '$lib/components/magic-ui/GradualSpacing.svelte';

	let mounted = $state(false);

	$effect(() => {
		mounted = true;
	});
</script>

{#if mounted}
	<div class="text-center px-4">
		<h1 class="text-4xl xs:text-5xl sm:text-6xl md:text-7xl lg:text-8xl font-bold mb-6 leading-tight">
			<GradualSpacing
				words="AMQ PLUS"
				class="amq-gradient-text"
				duration={0.6}
				delayMultiple={0.08}
			/>
		</h1>

		<div class="text-lg xs:text-xl sm:text-2xl md:text-3xl text-gray-600 font-medium mb-4" in:fade={{ duration: 600, delay: 800 }}>
			<span class="block sm:inline">Advanced Lobby Settings</span>
			<span class="hidden sm:inline mx-2 text-amq-primary">•</span>
			<span class="block sm:inline">Node Editor</span>
		</div>

		<div class="mt-6 text-base sm:text-lg md:text-xl text-gray-500 max-w-5xl mx-auto leading-relaxed px-2" in:fade={{ duration: 600, delay: 1000 }}>
			Design and customize AMQ lobby configurations with our powerful visual node-based editor
		</div>

		<!-- Decorative elements -->
		<div class="mt-4 flex justify-center space-x-2" in:fade={{ duration: 600, delay: 1200 }}>
			<div class="w-2 h-2 bg-amq-primary rounded-full animate-pulse"></div>
			<div class="w-2 h-2 bg-amq-secondary rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
			<div class="w-2 h-2 bg-amq-accent rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
		</div>
	</div>
{/if}

<style>
	@import '$lib/styles/amqplus.css';
</style>
