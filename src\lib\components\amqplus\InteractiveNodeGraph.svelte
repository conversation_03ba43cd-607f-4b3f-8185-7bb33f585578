<script>
	import { SvelteFlow, Controls, Background, MiniMap } from '@xyflow/svelte';
	import '@xyflow/svelte/dist/style.css';

	// Define initial nodes for the AMQ PLUS mockup (simplified for demo)
	let initialNodes = [
		{
			id: '1',
			type: 'default',
			position: { x: 100, y: 100 },
			data: {
				label: '🎮 Mode Settings',
				description: 'Scoring & Answering'
			},
			style:
				'background: rgba(117, 185, 223, 0.1); color: #1f2937; border: 2px solid rgba(117, 185, 223, 0.6); border-radius: 12px; padding: 12px; font-weight: 600; min-width: 160px;'
		},
		{
			id: '2',
			type: 'default',
			position: { x: 320, y: 100 },
			data: {
				label: '⚙️ General Settings',
				description: 'Players & Songs'
			},
			style:
				'background: rgba(223, 105, 117, 0.1); color: #1f2937; border: 2px solid rgba(223, 105, 117, 0.6); border-radius: 12px; padding: 12px; font-weight: 600; min-width: 160px;'
		},
		{
			id: '3',
			type: 'default',
			position: { x: 540, y: 100 },
			data: {
				label: '⏱️ Quiz Settings',
				description: 'Timing & Difficulty'
			},
			style:
				'background: rgba(117, 223, 139, 0.1); color: #1f2937; border: 2px solid rgba(117, 223, 139, 0.6); border-radius: 12px; padding: 12px; font-weight: 600; min-width: 160px;'
		},
		{
			id: '4',
			type: 'default',
			position: { x: 760, y: 100 },
			data: {
				label: '📺 Anime Settings',
				description: 'Filters & Vintage'
			},
			style:
				'background: rgba(223, 185, 117, 0.1); color: #1f2937; border: 2px solid rgba(223, 185, 117, 0.6); border-radius: 12px; padding: 12px; font-weight: 600; min-width: 160px;'
		},
		{
			id: '5',
			type: 'default',
			position: { x: 430, y: 250 },
			data: {
				label: '📤 Export Config',
				description: 'Generate lobby settings'
			},
			style:
				'background: rgba(181, 74, 90, 0.1); color: #1f2937; border: 2px solid rgba(181, 74, 90, 0.6); border-radius: 12px; padding: 12px; font-weight: 600; min-width: 160px;'
		}
	];

	// Define initial edges (connections between nodes)
	let initialEdges = [
		{
			id: 'e1-2',
			source: '1',
			target: '2',
			type: 'step',
			style: 'stroke: #75B9DF; stroke-width: 2; stroke-dasharray: 5,5;',
			animated: true
		},
		{
			id: 'e2-3',
			source: '2',
			target: '3',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2; stroke-dasharray: 5,5;',
			animated: true
		},
		{
			id: 'e3-4',
			source: '3',
			target: '4',
			type: 'step',
			style: 'stroke: #75DF8B; stroke-width: 2; stroke-dasharray: 5,5;',
			animated: true
		},
		{
			id: 'e2-5',
			source: '2',
			target: '5',
			type: 'step',
			style: 'stroke: #DF6975; stroke-width: 2; stroke-dasharray: 5,5;',
			animated: true
		},
		{
			id: 'e4-5',
			source: '4',
			target: '5',
			type: 'step',
			style: 'stroke: #DFB975; stroke-width: 2; stroke-dasharray: 5,5;',
			animated: true
		}
	];

	// Create reactive variables instead of stores
	let nodes = $state(initialNodes);
	let edges = $state(initialEdges);

	// Handle node drag end
	function onNodeDragStop(event) {
		console.log('Node dragged:', event.detail);
	}

	// Handle node click
	function onNodeClick(event) {
		console.log('Node clicked:', event.detail);
	}

	// Handle edge click
	function onEdgeClick(event) {
		console.log('Edge clicked:', event.detail);
	}
</script>

<div
	class="border-amq-light h-96 w-full overflow-hidden rounded-xl border bg-white/95 shadow-lg backdrop-blur-sm"
>
	<SvelteFlow
		{nodes}
		{edges}
		onnodedragstop={onNodeDragStop}
		onnodeclick={onNodeClick}
		onedgeclick={onEdgeClick}
		fitView
		proOptions={{ hideAttribution: true }}
	>
		<Background variant="dots" gap={20} size={1} color="rgba(223, 105, 117, 0.1)" />
	</SvelteFlow>
</div>

<style>
	:global(.svelte-flow__node) {
		font-family: 'Montserrat', sans-serif;
	}

	:global(.svelte-flow__controls) {
		background: rgba(255, 255, 255, 0.9);
		border: 1px solid rgba(223, 105, 117, 0.2);
		border-radius: 8px;
	}

	:global(.svelte-flow__controls button) {
		background: rgba(223, 105, 117, 0.1);
		border: none;
		color: #df6975;
		transition: all 0.2s ease;
	}

	:global(.svelte-flow__controls button:hover) {
		background: rgba(223, 105, 117, 0.2);
		transform: scale(1.05);
	}

	:global(.svelte-flow__minimap) {
		background: rgba(255, 255, 255, 0.9);
		border: 1px solid rgba(223, 105, 117, 0.2);
		border-radius: 8px;
	}

	:global(.svelte-flow__attribution) {
		background: rgba(255, 255, 255, 0.8);
		color: #df6975;
		font-size: 10px;
		padding: 2px 6px;
		border-radius: 4px;
	}
</style>
