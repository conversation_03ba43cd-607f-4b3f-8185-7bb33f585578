import { html } from 'satori-html';
import satori from 'satori';
import { Resvg } from '@resvg/resvg-js';

export async function GET({ url }) {
	const type = url.searchParams.get('type') || 'static';

	if (type === 'static') {
		return await generateAMQPlusOG();
	}

	// Default fallback
	return await generateAMQPlusOG();
}

async function loadFont() {
	const fontResponse = await fetch('http://pixeldrain.com/api/file/52yBhNXR');
	const fontBuffer = await fontResponse.arrayBuffer();
	return fontBuffer;
}

async function generateAMQPlusOG() {
	// Create simple SVG icons using AMQ PLUS theme colors
	// Create white SVG icons for better contrast
	const nodeIcon = 'data:image/svg+xml;base64,' + Buffer.from(`
		<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
			<circle cx="12" cy="12" r="3"/>
			<circle cx="12" cy="5" r="2"/>
			<circle cx="12" cy="19" r="2"/>
			<circle cx="5" cy="12" r="2"/>
			<circle cx="19" cy="12" r="2"/>
			<line x1="12" y1="9" x2="12" y2="15"/>
			<line x1="9" y1="12" x2="15" y2="12"/>
		</svg>
	`).toString('base64');

	const filterIcon = 'data:image/svg+xml;base64,' + Buffer.from(`
		<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
			<polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46"/>
		</svg>
	`).toString('base64');

	const settingsIcon = 'data:image/svg+xml;base64,' + Buffer.from(`
		<svg xmlns="http://www.w3.org/2000/svg" width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round">
			<circle cx="12" cy="12" r="3"/>
			<path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-3.5L19 8.5m-14 7L7.5 13m11 3.5L16.5 19m-9-9L5 8.5"/>
		</svg>
	`).toString('base64');

	const heartIcon = 'data:image/svg+xml;base64,' + Buffer.from(`
		<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="#DF6975" stroke="#DF6975" stroke-width="1" stroke-linecap="round" stroke-linejoin="round">
			<path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"/>
		</svg>
	`).toString('base64');

	const markup = html`
		<div style="
			display: flex;
			flex-direction: column;
			justify-content: center;
			align-items: center;
			width: 1200px;
			height: 630px;
			background: linear-gradient(135deg, #ffffff 0%, #fef2f2 25%, #fde2e8 50%, #fce7f3 75%, #fae5ff 100%);
			padding: 60px;
			position: relative;
			overflow: hidden;
		">
			<!-- AMQ PLUS themed decorative elements -->
			<div style="
				position: absolute;
				top: 60px;
				left: 80px;
				width: 140px;
				height: 140px;
				background: radial-gradient(circle, rgba(223, 105, 117, 0.25) 0%, rgba(223, 105, 117, 0.08) 70%, transparent 100%);
				border-radius: 50%;
				filter: blur(2px);
				display: flex;
			"></div>

			<div style="
				position: absolute;
				bottom: 80px;
				right: 100px;
				width: 180px;
				height: 180px;
				background: radial-gradient(circle, rgba(117, 185, 223, 0.2) 0%, rgba(117, 185, 223, 0.06) 70%, transparent 100%);
				border-radius: 50%;
				filter: blur(3px);
				display: flex;
			"></div>

			<div style="
				position: absolute;
				top: 120px;
				right: 180px;
				width: 100px;
				height: 100px;
				background: radial-gradient(circle, rgba(232, 137, 154, 0.18) 0%, rgba(232, 137, 154, 0.04) 70%, transparent 100%);
				border-radius: 50%;
				filter: blur(1.5px);
				display: flex;
			"></div>

			<div style="
				position: absolute;
				bottom: 200px;
				left: 150px;
				width: 60px;
				height: 60px;
				background: radial-gradient(circle, rgba(181, 74, 90, 0.15) 0%, rgba(181, 74, 90, 0.03) 70%, transparent 100%);
				border-radius: 50%;
				filter: blur(1px);
				display: flex;
			"></div>

			<!-- Main content -->
			<div style="
				display: flex;
				flex-direction: column;
				align-items: center;
				z-index: 10;
			">
				<!-- Main AMQ PLUS title -->
				<h1 style="
					font-family: 'Inter';
					font-size: 96px;
					font-weight: 900;
					background: linear-gradient(135deg, #DF6975 0%, #E8899A 35%, #75B9DF 70%, #B54A5A 100%);
					-webkit-background-clip: text;
					-webkit-text-fill-color: transparent;
					background-clip: text;
					margin: 0 0 16px 0;
					text-align: center;
					line-height: 0.9;
					letter-spacing: -0.04em;
					text-transform: uppercase;
				">AMQ PLUS</h1>

				<!-- Subtitle -->
				<p style="
					font-family: 'Inter';
					font-size: 32px;
					color: #75B9DF;
					margin: 0 0 12px 0;
					text-align: center;
					font-weight: 600;
					letter-spacing: -0.01em;
				">Advanced Lobby Settings</p>

				<!-- Description -->
				<p style="
					font-family: 'Inter';
					font-size: 20px;
					color: #8B7B7A;
					margin: 0 0 40px 0;
					text-align: center;
					font-weight: 500;
					max-width: 800px;
					line-height: 1.4;
				">Visual Node-Based Editor for Anime Music Quiz</p>

				<!-- Feature icons -->
				<div style="
					display: flex;
					gap: 48px;
					align-items: center;
					justify-content: center;
					margin-bottom: 36px;
				">
					<div style="
						display: flex;
						flex-direction: column;
						align-items: center;
						text-align: center;
					">
						<div style="
							width: 80px;
							height: 80px;
							background: linear-gradient(135deg, #DF6975, #B54A5A);
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-bottom: 16px;
							box-shadow: 0 12px 35px rgba(223, 105, 117, 0.4), 0 4px 15px rgba(0, 0, 0, 0.1);
							border: 4px solid rgba(255, 255, 255, 0.9);
						">
							<img src="${nodeIcon}" style="width: 40px; height: 40px;" alt="Node Editor" />
						</div>
						<span style="
							font-family: 'Inter';
							font-size: 18px;
							color: #DF6975;
							font-weight: 800;
						">Node Editor</span>
					</div>

					<div style="
						display: flex;
						flex-direction: column;
						align-items: center;
						text-align: center;
					">
						<div style="
							width: 80px;
							height: 80px;
							background: linear-gradient(135deg, #75DF8B, #5AC46B);
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-bottom: 16px;
							box-shadow: 0 12px 35px rgba(117, 223, 139, 0.4), 0 4px 15px rgba(0, 0, 0, 0.1);
							border: 4px solid rgba(255, 255, 255, 0.9);
						">
							<img src="${filterIcon}" style="width: 40px; height: 40px;" alt="Advanced Filters" />
						</div>
						<span style="
							font-family: 'Inter';
							font-size: 18px;
							color: #75DF8B;
							font-weight: 800;
						">Advanced Filters</span>
					</div>

					<div style="
						display: flex;
						flex-direction: column;
						align-items: center;
						text-align: center;
					">
						<div style="
							width: 80px;
							height: 80px;
							background: linear-gradient(135deg, #75B9DF, #5A9BD4);
							border-radius: 50%;
							display: flex;
							align-items: center;
							justify-content: center;
							margin-bottom: 16px;
							box-shadow: 0 12px 35px rgba(117, 185, 223, 0.4), 0 4px 15px rgba(0, 0, 0, 0.1);
							border: 4px solid rgba(255, 255, 255, 0.9);
						">
							<img src="${settingsIcon}" style="width: 40px; height: 40px;" alt="Custom Rules" />
						</div>
						<span style="
							font-family: 'Inter';
							font-size: 18px;
							color: #75B9DF;
							font-weight: 800;
						">Custom Rules</span>
					</div>
				</div>

				<!-- Feature badges -->
				<div style="
					display: flex;
					gap: 18px;
					align-items: center;
					justify-content: center;
					flex-wrap: wrap;
				">
					<div style="
						padding: 10px 18px;
						background: rgba(223, 105, 117, 0.15);
						border-radius: 25px;
						display: flex;
						align-items: center;
						justify-content: center;
						backdrop-filter: blur(10px);
						border: 2px solid rgba(223, 105, 117, 0.4);
						box-shadow: 0 4px 15px rgba(223, 105, 117, 0.2);
					">
						<span style="
							font-family: 'Inter';
							font-size: 15px;
							font-weight: 800;
							color: #B54A5A;
						">🎵 Song Settings</span>
					</div>

					<div style="
						padding: 10px 18px;
						background: rgba(117, 223, 139, 0.15);
						border-radius: 25px;
						display: flex;
						align-items: center;
						justify-content: center;
						backdrop-filter: blur(10px);
						border: 2px solid rgba(117, 223, 139, 0.4);
						box-shadow: 0 4px 15px rgba(117, 223, 139, 0.2);
					">
						<span style="
							font-family: 'Inter';
							font-size: 15px;
							font-weight: 800;
							color: #5AC46B;
						">🏆 Scoring</span>
					</div>

					<div style="
						padding: 10px 18px;
						background: rgba(117, 185, 223, 0.15);
						border-radius: 25px;
						display: flex;
						align-items: center;
						justify-content: center;
						backdrop-filter: blur(10px);
						border: 2px solid rgba(117, 185, 223, 0.4);
						box-shadow: 0 4px 15px rgba(117, 185, 223, 0.2);
					">
						<span style="
							font-family: 'Inter';
							font-size: 15px;
							font-weight: 800;
							color: #5A9BD4;
						">👥 Players</span>
					</div>

					<div style="
						padding: 10px 18px;
						background: rgba(232, 137, 154, 0.15);
						border-radius: 25px;
						display: flex;
						align-items: center;
						justify-content: center;
						backdrop-filter: blur(10px);
						border: 2px solid rgba(232, 137, 154, 0.4);
						box-shadow: 0 4px 15px rgba(232, 137, 154, 0.2);
					">
						<span style="
							font-family: 'Inter';
							font-size: 15px;
							font-weight: 800;
							color: #D4758A;
						">📤 Export</span>
					</div>
				</div>
			</div>
		</div>
		`;

	const fontData = await loadFont();

	const svg = await satori(markup, {
		width: 1200,
		height: 630,
		fonts: [
			{
				name: 'Inter',
				data: fontData,
				weight: 400,
				style: 'normal',
			},
		]
	});

	const resvg = new Resvg(svg);
	const pngData = resvg.render();
	const pngBuffer = pngData.asPng();

	return new Response(pngBuffer, {
		headers: {
			'Content-Type': 'image/png',
			'Cache-Control': 'public, max-age=31536000, immutable'
		}
	});
}
