<script>
	let { settings } = $props();

	// Generate year options for vintage
	const generateYearOptions = () => {
		const years = [];
		for (let year = 1944; year <= 2025; year++) {
			const seasons = ['Winter', 'Spring', 'Summer', 'Fall'];
			seasons.forEach(season => {
				years.push(`${season} ${year}`);
			});
		}
		return years;
	};

	const yearOptions = generateYearOptions();

	// Handle score ranges
	const updatePlayerScore = (type, value) => {
		const numValue = parseInt(value) || 1;
		if (type === 'min') {
			if (numValue >= 1 && numValue <= settings.playerScore.max) {
				settings.playerScore.min = numValue;
			}
		} else {
			if (numValue >= settings.playerScore.min && numValue <= 10) {
				settings.playerScore.max = numValue;
			}
		}
	};

	const updateAnimeScore = (type, value) => {
		const numValue = parseInt(value) || 2;
		if (type === 'min') {
			if (numValue >= 2 && numValue <= settings.animeScore.max) {
				settings.animeScore.min = numValue;
			}
		} else {
			if (numValue >= settings.animeScore.min && numValue <= 10) {
				settings.animeScore.max = numValue;
			}
		}
	};

	// Sample genres and tags (in a real app, these would come from an API)
	const availableGenres = [
		'Action', 'Adventure', 'Comedy', 'Drama', 'Fantasy', 'Horror', 'Mystery', 'Romance', 
		'Sci-Fi', 'Slice of Life', 'Sports', 'Supernatural', 'Thriller', 'Mecha', 'Music'
	];

	const availableTags = [
		'School', 'Magic', 'Demons', 'Military', 'Historical', 'Parody', 'Super Power',
		'Vampire', 'Harem', 'Josei', 'Seinen', 'Shoujo', 'Shounen', 'Game', 'Martial Arts'
	];

	// Handle genre/tag management
	const addGenre = (genre, type) => {
		if (!settings.genres[type].includes(genre)) {
			settings.genres[type] = [...settings.genres[type], genre];
		}
	};

	const removeGenre = (genre, type) => {
		settings.genres[type] = settings.genres[type].filter(g => g !== genre);
	};

	const addTag = (tag, type) => {
		if (!settings.tags[type].includes(tag)) {
			settings.tags[type] = [...settings.tags[type], tag];
		}
	};

	const removeTag = (tag, type) => {
		settings.tags[type] = settings.tags[type].filter(t => t !== tag);
	};
</script>

<div class="space-y-4">
	<!-- Player Score Range -->
	<div class="space-y-2">
		<div class="text-sm font-medium text-gray-700">Player Score Range</div>
		<div class="grid grid-cols-2 gap-2">
			<div>
				<label for="playerScoreMin" class="text-xs text-gray-600">Min</label>
				<input
					id="playerScoreMin"
					type="number"
					value={settings.playerScore.min}
					oninput={(e) => updatePlayerScore('min', e.target.value)}
					min="1"
					max="10"
					class="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
				/>
			</div>
			<div>
				<label for="playerScoreMax" class="text-xs text-gray-600">Max</label>
				<input
					id="playerScoreMax"
					type="number"
					value={settings.playerScore.max}
					oninput={(e) => updatePlayerScore('max', e.target.value)}
					min="1"
					max="10"
					class="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
				/>
			</div>
		</div>
	</div>

	<!-- Anime Score Range -->
	<div class="space-y-2">
		<div class="text-sm font-medium text-gray-700">Anime Score Range</div>
		<div class="grid grid-cols-2 gap-2">
			<div>
				<label for="animeScoreMin" class="text-xs text-gray-600">Min</label>
				<input
					id="animeScoreMin"
					type="number"
					value={settings.animeScore.min}
					oninput={(e) => updateAnimeScore('min', e.target.value)}
					min="2"
					max="10"
					class="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
				/>
			</div>
			<div>
				<label for="animeScoreMax" class="text-xs text-gray-600">Max</label>
				<input
					id="animeScoreMax"
					type="number"
					value={settings.animeScore.max}
					oninput={(e) => updateAnimeScore('max', e.target.value)}
					min="2"
					max="10"
					class="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
				/>
			</div>
		</div>
	</div>

	<!-- Vintage -->
	<div class="space-y-2">
		<div class="text-sm font-medium text-gray-700">Vintage</div>
		<div class="grid grid-cols-2 gap-2">
			<div>
				<label for="vintageFrom" class="text-xs text-gray-600">From</label>
				<select
					id="vintageFrom"
					bind:value={settings.vintage.from}
					class="w-full px-2 py-1 text-xs border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
				>
					{#each yearOptions as year}
						<option value={year}>{year}</option>
					{/each}
				</select>
			</div>
			<div>
				<label for="vintageTo" class="text-xs text-gray-600">To</label>
				<select
					id="vintageTo"
					bind:value={settings.vintage.to}
					class="w-full px-2 py-1 text-xs border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
				>
					{#each yearOptions as year}
						<option value={year}>{year}</option>
					{/each}
				</select>
			</div>
		</div>
	</div>

	<!-- Type -->
	<div class="space-y-2">
		<div class="text-sm font-medium text-gray-700">Type</div>
		<div class="grid grid-cols-2 gap-2">
			<label class="flex items-center space-x-2">
				<input type="checkbox" bind:checked={settings.type.tv} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
				<span class="text-xs">TV</span>
			</label>
			<label class="flex items-center space-x-2">
				<input type="checkbox" bind:checked={settings.type.movie} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
				<span class="text-xs">Movie</span>
			</label>
			<label class="flex items-center space-x-2">
				<input type="checkbox" bind:checked={settings.type.ova} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
				<span class="text-xs">OVA</span>
			</label>
			<label class="flex items-center space-x-2">
				<input type="checkbox" bind:checked={settings.type.ona} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
				<span class="text-xs">ONA</span>
			</label>
			<label class="flex items-center space-x-2">
				<input type="checkbox" bind:checked={settings.type.special} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
				<span class="text-xs">Special</span>
			</label>
		</div>
	</div>

	<!-- Genres -->
	<div class="space-y-2">
		<div class="text-sm font-medium text-gray-700">Genres</div>
		<div class="text-xs text-gray-600 mb-2">Select genres to include, exclude, or optionally include</div>

		<!-- Genre selection dropdown -->
		<select
			onchange={(e) => {
				if (e.target.value) {
					addGenre(e.target.value, 'include');
					e.target.value = '';
				}
			}}
			class="w-full px-2 py-1 text-xs border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
		>
			<option value="">Add genre...</option>
			{#each availableGenres as genre}
				<option value={genre}>{genre}</option>
			{/each}
		</select>

		<!-- Include -->
		{#if settings.genres.include.length > 0}
			<div class="space-y-1">
				<div class="text-xs font-medium text-green-600">Include</div>
				<div class="flex flex-wrap gap-1">
					{#each settings.genres.include as genre}
						<span class="inline-flex items-center px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
							{genre}
							<button type="button" onclick={() => removeGenre(genre, 'include')} class="ml-1 text-green-600 hover:text-green-800">×</button>
						</span>
					{/each}
				</div>
			</div>
		{/if}

		<!-- Exclude -->
		{#if settings.genres.exclude.length > 0}
			<div class="space-y-1">
				<div class="text-xs font-medium text-red-600">Exclude</div>
				<div class="flex flex-wrap gap-1">
					{#each settings.genres.exclude as genre}
						<span class="inline-flex items-center px-2 py-1 text-xs bg-red-100 text-red-800 rounded">
							{genre}
							<button type="button" onclick={() => removeGenre(genre, 'exclude')} class="ml-1 text-red-600 hover:text-red-800">×</button>
						</span>
					{/each}
				</div>
			</div>
		{/if}

		<!-- Optional -->
		{#if settings.genres.optional.length > 0}
			<div class="space-y-1">
				<div class="text-xs font-medium text-blue-600">Optional</div>
				<div class="flex flex-wrap gap-1">
					{#each settings.genres.optional as genre}
						<span class="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
							{genre}
							<button type="button" onclick={() => removeGenre(genre, 'optional')} class="ml-1 text-blue-600 hover:text-blue-800">×</button>
						</span>
					{/each}
				</div>
			</div>
		{/if}
	</div>

	<!-- Tags -->
	<div class="space-y-2">
		<div class="text-sm font-medium text-gray-700">Tags</div>
		<div class="text-xs text-gray-600 mb-2">Select tags to include, exclude, or optionally include</div>

		<!-- Tag selection dropdown -->
		<select
			onchange={(e) => {
				if (e.target.value) {
					addTag(e.target.value, 'include');
					e.target.value = '';
				}
			}}
			class="w-full px-2 py-1 text-xs border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
		>
			<option value="">Add tag...</option>
			{#each availableTags as tag}
				<option value={tag}>{tag}</option>
			{/each}
		</select>

		<!-- Include -->
		{#if settings.tags.include.length > 0}
			<div class="space-y-1">
				<div class="text-xs font-medium text-green-600">Include</div>
				<div class="flex flex-wrap gap-1">
					{#each settings.tags.include as tag}
						<span class="inline-flex items-center px-2 py-1 text-xs bg-green-100 text-green-800 rounded">
							{tag}
							<button type="button" onclick={() => removeTag(tag, 'include')} class="ml-1 text-green-600 hover:text-green-800">×</button>
						</span>
					{/each}
				</div>
			</div>
		{/if}

		<!-- Exclude -->
		{#if settings.tags.exclude.length > 0}
			<div class="space-y-1">
				<div class="text-xs font-medium text-red-600">Exclude</div>
				<div class="flex flex-wrap gap-1">
					{#each settings.tags.exclude as tag}
						<span class="inline-flex items-center px-2 py-1 text-xs bg-red-100 text-red-800 rounded">
							{tag}
							<button type="button" onclick={() => removeTag(tag, 'exclude')} class="ml-1 text-red-600 hover:text-red-800">×</button>
						</span>
					{/each}
				</div>
			</div>
		{/if}

		<!-- Optional -->
		{#if settings.tags.optional.length > 0}
			<div class="space-y-1">
				<div class="text-xs font-medium text-blue-600">Optional</div>
				<div class="flex flex-wrap gap-1">
					{#each settings.tags.optional as tag}
						<span class="inline-flex items-center px-2 py-1 text-xs bg-blue-100 text-blue-800 rounded">
							{tag}
							<button type="button" onclick={() => removeTag(tag, 'optional')} class="ml-1 text-blue-600 hover:text-blue-800">×</button>
						</span>
					{/each}
				</div>
			</div>
		{/if}
	</div>
</div>
