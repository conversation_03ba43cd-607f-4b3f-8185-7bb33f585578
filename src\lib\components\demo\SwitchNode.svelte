<script>
	import { <PERSON><PERSON>, Position } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Switch } from '$lib/components/ui/switch';
	import { Label } from '$lib/components/ui/label';

	let { data } = $props();
	let switchValue = $state(data.value || false);

	function handleSwitchChange(checked) {
		switchValue = checked;
		data.value = checked;
	}
</script>

<div class="switch-node min-w-64">
	<Handle type="target" position={Position.Left} />
	
	<Card class="border-2 border-purple-300 bg-purple-50/50">
		<CardHeader class="pb-2">
			<CardTitle class="flex items-center gap-2 text-sm font-semibold text-purple-800">
				<span class="text-lg">🔄</span>
				{data.label || 'Toggle Switch'}
			</CardTitle>
		</CardHeader>
		<CardContent class="pt-0">
			<div class="space-y-3">
				<div class="flex items-center space-x-2">
					<Switch
						checked={switchValue}
						onCheckedChange={handleSwitchChange}
						id="switch-toggle"
					/>
					<Label for="switch-toggle" class="text-sm text-purple-700">
						{data.switchLabel || 'Enable feature'}
					</Label>
				</div>
				<div class="text-xs text-purple-600 bg-purple-100 p-2 rounded">
					Status: <span class="font-medium">{switchValue ? 'ON' : 'OFF'}</span>
				</div>
			</div>
		</CardContent>
	</Card>
	
	<Handle type="source" position={Position.Right} />
</div>

<style>
	.switch-node {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}
</style>
