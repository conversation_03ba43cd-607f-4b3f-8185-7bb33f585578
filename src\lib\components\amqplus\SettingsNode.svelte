<script>
	import { <PERSON><PERSON>, Position } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import ModeSettings from './settings/ModeSettings.svelte';
	import GeneralSettings from './settings/GeneralSettings.svelte';
	import QuizSettings from './settings/QuizSettings.svelte';
	import AnimeSettings from './settings/AnimeSettings.svelte';

	let { data } = $props();

	// Get the color with opacity for background
	const getBackgroundColor = (color) => {
		// Convert hex to rgba with low opacity
		const hex = color.replace('#', '');
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		return `rgba(${r}, ${g}, ${b}, 0.1)`;
	};

	// Get the border color with higher opacity
	const getBorderColor = (color) => {
		const hex = color.replace('#', '');
		const r = parseInt(hex.substr(0, 2), 16);
		const g = parseInt(hex.substr(2, 2), 16);
		const b = parseInt(hex.substr(4, 2), 16);
		return `rgba(${r}, ${g}, ${b}, 0.6)`;
	};

	const backgroundColor = $derived(getBackgroundColor(data.color));
	const borderColor = $derived(getBorderColor(data.color));
</script>

<div 
	class="settings-node min-w-80 max-w-96"
	style="background: {backgroundColor}; border: 2px solid {borderColor}; border-radius: 12px;"
>
	<!-- Input handle -->
	{#if data.zone !== 'mode'}
		<Handle type="target" position={Position.Left} />
	{/if}
	
	<!-- Output handle -->
	{#if data.zone !== 'anime'}
		<Handle type="source" position={Position.Right} />
	{/if}

	<Card class="border-0 shadow-none bg-transparent">
		<CardHeader class="pb-3">
			<CardTitle class="flex items-center gap-2 text-lg font-semibold text-gray-800">
				<span class="text-xl">{data.icon}</span>
				{data.title}
			</CardTitle>
		</CardHeader>
		<CardContent class="pt-0">
			{#if data.zone === 'mode'}
				<ModeSettings bind:settings={data.settings} />
			{:else if data.zone === 'general'}
				<GeneralSettings bind:settings={data.settings} />
			{:else if data.zone === 'quiz'}
				<QuizSettings bind:settings={data.settings} />
			{:else if data.zone === 'anime'}
				<AnimeSettings bind:settings={data.settings} />
			{/if}
		</CardContent>
	</Card>
</div>

<style>
	.settings-node {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}
	
	:global(.settings-node .svelte-flow__handle) {
		width: 12px;
		height: 12px;
		background: #6b7280;
		border: 2px solid white;
	}
	
	:global(.settings-node .svelte-flow__handle.svelte-flow__handle-left) {
		left: -6px;
	}
	
	:global(.settings-node .svelte-flow__handle.svelte-flow__handle-right) {
		right: -6px;
	}
</style>
