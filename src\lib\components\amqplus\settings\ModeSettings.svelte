<script>
	let { settings } = $props();

	const scoringOptions = [
		{ value: 'count', label: 'Count' },
		{ value: 'hint', label: 'Hint' },
		{ value: 'speed', label: 'Speed' }
	];

	const answeringOptions = [
		{ value: 'typing', label: 'Typing' },
		{ value: 'mix', label: 'Mix' },
		{ value: 'multiple-choice', label: 'Multiple Choice' }
	];
</script>

<div class="space-y-4">
	<!-- Scoring Setting -->
	<div class="space-y-2">
		<label for="scoring-select" class="text-sm font-medium text-gray-700">Scoring</label>
		<select
			id="scoring-select"
			bind:value={settings.scoring}
			class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
		>
			{#each scoringOptions as option}
				<option value={option.value}>{option.label}</option>
			{/each}
		</select>
	</div>

	<!-- Answering Setting -->
	<div class="space-y-2">
		<label for="answering-select" class="text-sm font-medium text-gray-700">Answering</label>
		<select
			id="answering-select"
			bind:value={settings.answering}
			class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
		>
			{#each answeringOptions as option}
				<option value={option.value}>{option.label}</option>
			{/each}
		</select>
	</div>
</div>
