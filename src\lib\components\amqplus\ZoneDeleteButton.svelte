<script>
	let { data } = $props();
</script>

<div
	class="zone-delete-button-overlay relative"
	style="width: {data.width}px; height: {data.height}px; pointer-events: none;"
>
	<!-- Delete button for zone with individual nodes - positioned at top right -->
	{#if data.onDelete}
		<button
			class=" absolute -top-2 -right-2 z-30 flex h-8 w-8 cursor-pointer items-center justify-center rounded-full border-2 border-white bg-red-500 text-sm font-bold text-white shadow-lg transition-colors hover:bg-red-600"
			style="pointer-events: auto;"
			onclick={(e) => {
				e.stopPropagation();
				e.preventDefault();
				data.onDelete({ detail: { zoneId: data.zone, timestamp: data.timestamp } });
			}}
			title="Delete all individual nodes in this zone"
		>
			×
		</button>
	{/if}
</div>

<style>
	.zone-delete-button-overlay {
		font-family: -apple-system, BlinkMacSystemFont, 'Se<PERSON>e UI', <PERSON><PERSON>, sans-serif;
	}
</style>
