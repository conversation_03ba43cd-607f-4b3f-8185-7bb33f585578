<script>
	let { settings } = $props();

	// Handle sample point range
	const updateSamplePoint = (type, value) => {
		const numValue = parseInt(value) || 0;
		if (type === 'start') {
			if (numValue >= 0 && numValue <= settings.samplePoint.end) {
				settings.samplePoint.start = numValue;
			}
		} else {
			if (numValue >= settings.samplePoint.start && numValue <= 100) {
				settings.samplePoint.end = numValue;
			}
		}
	};
</script>

<div class="space-y-4">
	<!-- Guess Time -->
	<div class="space-y-2">
		<label for="guessTime" class="text-sm font-medium text-gray-700">Guess Time (seconds)</label>
		<input
			id="guessTime"
			type="number"
			bind:value={settings.guessTime}
			min="1"
			max="120"
			class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
		/>
	</div>

	<!-- Extra Guess Time -->
	<div class="space-y-2">
		<label for="extraGuessTime" class="text-sm font-medium text-gray-700">Extra Guess Time (seconds)</label>
		<input
			id="extraGuessTime"
			type="number"
			bind:value={settings.extraGuessTime}
			min="0"
			max="60"
			class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
		/>
	</div>

	<!-- Sample Point -->
	<div class="space-y-2">
		<div class="text-sm font-medium text-gray-700">Sample Point (%)</div>
		<div class="grid grid-cols-2 gap-2">
			<div>
				<label for="samplePointStart" class="text-xs text-gray-600">Start</label>
				<input
					id="samplePointStart"
					type="number"
					value={settings.samplePoint.start}
					oninput={(e) => updateSamplePoint('start', e.target.value)}
					min="0"
					max="100"
					class="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
				/>
			</div>
			<div>
				<label for="samplePointEnd" class="text-xs text-gray-600">End</label>
				<input
					id="samplePointEnd"
					type="number"
					value={settings.samplePoint.end}
					oninput={(e) => updateSamplePoint('end', e.target.value)}
					min="0"
					max="100"
					class="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
				/>
			</div>
		</div>
	</div>

	<!-- Playback Speed -->
	<div class="space-y-2">
		<label for="playbackSpeed" class="text-sm font-medium text-gray-700">Playback Speed</label>
		<select
			id="playbackSpeed"
			bind:value={settings.playbackSpeed}
			class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
		>
			<option value={0.25}>0.25x</option>
			<option value={0.5}>0.5x</option>
			<option value={0.75}>0.75x</option>
			<option value={1.0}>1.0x</option>
			<option value={1.25}>1.25x</option>
			<option value={1.5}>1.5x</option>
			<option value={2.0}>2.0x</option>
		</select>
	</div>

	<!-- Song Difficulty -->
	<div class="space-y-2">
		<div class="text-sm font-medium text-gray-700">Song Difficulty</div>
		<div class="grid grid-cols-3 gap-2">
			<label class="flex items-center space-x-2">
				<input type="checkbox" bind:checked={settings.songDifficulty.easy} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
				<span class="text-xs">Easy</span>
			</label>
			<label class="flex items-center space-x-2">
				<input type="checkbox" bind:checked={settings.songDifficulty.medium} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
				<span class="text-xs">Medium</span>
			</label>
			<label class="flex items-center space-x-2">
				<input type="checkbox" bind:checked={settings.songDifficulty.hard} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
				<span class="text-xs">Hard</span>
			</label>
		</div>
	</div>

	<!-- Song Popularity -->
	<div class="space-y-2">
		<div class="text-sm font-medium text-gray-700">Song Popularity</div>
		<div class="grid grid-cols-3 gap-2">
			<label class="flex items-center space-x-2">
				<input type="checkbox" bind:checked={settings.songPopularity.disliked} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
				<span class="text-xs">Disliked</span>
			</label>
			<label class="flex items-center space-x-2">
				<input type="checkbox" bind:checked={settings.songPopularity.mixed} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
				<span class="text-xs">Mixed</span>
			</label>
			<label class="flex items-center space-x-2">
				<input type="checkbox" bind:checked={settings.songPopularity.liked} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
				<span class="text-xs">Liked</span>
			</label>
		</div>
	</div>

	<!-- Modifiers -->
	<div class="space-y-2">
		<div class="text-sm font-medium text-gray-700">Modifiers</div>
		<div class="space-y-2">
			<label class="flex items-center space-x-2">
				<input type="checkbox" bind:checked={settings.modifiers.skipGuessing} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
				<span class="text-xs">Skip Guessing</span>
			</label>
			<label class="flex items-center space-x-2">
				<input type="checkbox" bind:checked={settings.modifiers.skipResults} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
				<span class="text-xs">Skip Results</span>
			</label>
			<label class="flex items-center space-x-2">
				<input type="checkbox" bind:checked={settings.modifiers.queueing} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
				<span class="text-xs">Queueing</span>
			</label>
		</div>
	</div>
</div>
