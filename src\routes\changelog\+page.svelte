<script>
	import { browser } from '$app/environment';
	import { Calendar, Tag, Zap, Wrench, Star, Users } from 'lucide-svelte';

	const title = $derived('Changelog - AMQ PLUS');
	const description = $derived('Stay updated with the latest features, improvements, and fixes in AMQ PLUS - your advanced lobby settings editor for Anime Music Quiz.');
	const imageUrl = $derived(browser ? `${window.location.origin}/api/og` : 'https://amqplus.moe/api/og');
	const canonicalUrl = $derived(browser ? window.location.href : 'https://amqplus.moe/changelog');

	const changelogEntries = [
		{
			version: '2.1.0',
			date: '2024-12-15',
			type: 'feature',
			title: 'Advanced Node Connections',
			description: 'Introduced sophisticated node connection system with conditional logic support. Users can now create complex lobby configurations with if-then-else conditions.',
			highlights: [
				'Conditional node connections',
				'Logic gate nodes (AND, OR, NOT)',
				'Enhanced visual feedback for connections',
				'Improved node validation system'
			],
			icon: Zap
		},
		{
			version: '2.0.5',
			date: '2024-12-08',
			type: 'improvement',
			title: 'Performance Optimizations',
			description: 'Significant performance improvements for large node graphs and better memory management for complex configurations.',
			highlights: [
				'50% faster node rendering',
				'Reduced memory usage by 30%',
				'Smoother drag and drop interactions',
				'Optimized connection calculations'
			],
			icon: Wrench
		},
		{
			version: '2.0.0',
			date: '2024-11-28',
			type: 'major',
			title: 'Visual Node Editor Launch',
			description: 'Complete redesign with our new interactive node-based editor. Create AMQ lobby configurations visually with drag-and-drop functionality.',
			highlights: [
				'Interactive node-based interface',
				'Real-time configuration preview',
				'Drag and drop node creation',
				'Visual connection system',
				'Export to AMQ format'
			],
			icon: Star
		},
		{
			version: '1.8.2',
			date: '2024-11-15',
			type: 'feature',
			title: 'Quick Start Templates',
			description: 'Added pre-built templates for common AMQ lobby configurations to help users get started quickly.',
			highlights: [
				'Competitive tournament template',
				'Casual listening party setup',
				'Training mode configuration',
				'Custom difficulty templates'
			],
			icon: Zap
		},
		{
			version: '1.7.1',
			date: '2024-11-02',
			type: 'improvement',
			title: 'Enhanced User Experience',
			description: 'Improved overall user interface with better accessibility and mobile responsiveness.',
			highlights: [
				'Mobile-friendly design',
				'Improved keyboard navigation',
				'Better screen reader support',
				'Enhanced color contrast'
			],
			icon: Users
		},
		{
			version: '1.6.0',
			date: '2024-10-20',
			type: 'feature',
			title: 'Advanced Filtering Options',
			description: 'Introduced comprehensive filtering system for anime selection with genre, year, and popularity filters.',
			highlights: [
				'Genre-based filtering',
				'Year range selection',
				'Popularity score filters',
				'Custom tag system'
			],
			icon: Tag
		}
	];

	function getTypeColor(type) {
		switch (type) {
			case 'major':
				return 'bg-purple-100 text-purple-800 border-purple-200';
			case 'feature':
				return 'bg-green-100 text-green-800 border-green-200';
			case 'improvement':
				return 'bg-blue-100 text-blue-800 border-blue-200';
			default:
				return 'bg-gray-100 text-gray-800 border-gray-200';
		}
	}

	function formatDate(dateString) {
		return new Date(dateString).toLocaleDateString('en-US', {
			year: 'numeric',
			month: 'long',
			day: 'numeric'
		});
	}
</script>

<svelte:head>
	<title>{title}</title>
	<meta name="description" content={description} />
	
	<!-- Open Graph / Facebook -->
	<meta property="og:type" content="website" />
	<meta property="og:url" content={canonicalUrl} />
	<meta property="og:title" content={title} />
	<meta property="og:description" content={description} />
	<meta property="og:image" content={imageUrl} />
	
	<!-- Twitter -->
	<meta property="twitter:card" content="summary_large_image" />
	<meta property="twitter:url" content={canonicalUrl} />
	<meta property="twitter:title" content={title} />
	<meta property="twitter:description" content={description} />
	<meta property="twitter:image" content={imageUrl} />
	
	<link rel="canonical" href={canonicalUrl} />
</svelte:head>

<div class="min-h-screen bg-gradient-to-br from-rose-50 to-pink-50 py-12">
	<div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
		<!-- Header -->
		<div class="text-center mb-12">
			<h1 class="text-4xl sm:text-5xl font-bold text-gray-900 mb-4">
				<span class="bg-gradient-to-r from-rose-600 to-pink-600 bg-clip-text text-transparent">
					Changelog
				</span>
			</h1>
			<p class="text-xl text-gray-600 max-w-2xl mx-auto">
				Stay updated with the latest features, improvements, and fixes in AMQ PLUS
			</p>
		</div>

		<!-- Changelog Entries -->
		<div class="space-y-8">
			{#each changelogEntries as entry}
				{@const Icon = entry.icon}
				<div class="bg-white/80 backdrop-blur-sm rounded-xl shadow-lg border border-gray-200/50 overflow-hidden hover:shadow-xl transition-shadow duration-300">
					<div class="p-6 sm:p-8">
						<!-- Header -->
						<div class="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
							<div class="flex items-center space-x-3 mb-2 sm:mb-0">
								<div class="p-2 bg-rose-100 rounded-lg">
									<Icon class="w-5 h-5 text-rose-600" />
								</div>
								<div>
									<h2 class="text-xl font-semibold text-gray-900">{entry.title}</h2>
									<div class="flex items-center space-x-2 text-sm text-gray-500">
										<span class="font-medium">v{entry.version}</span>
										<span>•</span>
										<div class="flex items-center space-x-1">
											<Calendar size={14} />
											<span>{formatDate(entry.date)}</span>
										</div>
									</div>
								</div>
							</div>
							<span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium border {getTypeColor(entry.type)}">
								{entry.type.charAt(0).toUpperCase() + entry.type.slice(1)}
							</span>
						</div>

						<!-- Description -->
						<p class="text-gray-700 mb-4 leading-relaxed">
							{entry.description}
						</p>

						<!-- Highlights -->
						<div class="space-y-2">
							<h3 class="text-sm font-medium text-gray-900">Key Highlights:</h3>
							<ul class="grid grid-cols-1 sm:grid-cols-2 gap-2">
								{#each entry.highlights as highlight}
									<li class="flex items-center space-x-2 text-sm text-gray-600">
										<div class="w-1.5 h-1.5 bg-rose-500 rounded-full flex-shrink-0"></div>
										<span>{highlight}</span>
									</li>
								{/each}
							</ul>
						</div>
					</div>
				</div>
			{/each}
		</div>
	</div>
</div>
