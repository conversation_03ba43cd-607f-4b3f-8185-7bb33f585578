<script>
	import { Input } from '$lib/components/ui/input/index.js';
	import * as Select from '$lib/components/ui/select/index.js';
	import { createEventDispatcher } from 'svelte';

	let { 
		value = $bindable(),
		type = 'text',
		options = [],
		placeholder = '',
		min,
		max,
		isEditing = $bindable(false),
		class: className = ''
	} = $props();

	const dispatch = createEventDispatcher();
	let tempValue = $state(value);
	let inputElement = $state(null);

	// Watch for value changes from parent
	$effect(() => {
		tempValue = value;
	});

	// Focus input when editing starts
	$effect(() => {
		if (isEditing && inputElement) {
			inputElement.focus();
			if (type === 'number') {
				inputElement.select();
			}
		}
	});

	function startEdit() {
		tempValue = value;
		isEditing = true;
	}

	function saveEdit() {
		value = tempValue;
		isEditing = false;
		dispatch('save', { value: tempValue });
	}

	function cancelEdit() {
		tempValue = value;
		isEditing = false;
	}

	function handleKeydown(event) {
		if (event.key === 'Enter') {
			saveEdit();
		} else if (event.key === 'Escape') {
			cancelEdit();
		}
	}

	function handleBlur() {
		saveEdit();
	}

	function formatDisplayValue(val) {
		if (type === 'select' && options.length > 0) {
			const option = options.find(opt => opt.value === val);
			return option ? option.label : val;
		}
		return val || placeholder;
	}
</script>

{#if isEditing}
	{#if type === 'select'}
		<Select.Root bind:value={tempValue} onValueChange={() => setTimeout(saveEdit, 0)}>
			<Select.Trigger class="h-6 text-xs px-2 {className}">
				{tempValue ? options.find(opt => opt.value === tempValue)?.label || tempValue : placeholder}
			</Select.Trigger>
			<Select.Content>
				{#each options as option}
					<Select.Item value={option.value}>{option.label}</Select.Item>
				{/each}
			</Select.Content>
		</Select.Root>
	{:else}
		<Input
			bind:this={inputElement}
			bind:value={tempValue}
			{type}
			{placeholder}
			{min}
			{max}
			class="h-6 text-xs px-2 {className}"
			onkeydown={handleKeydown}
			onblur={handleBlur}
		/>
	{/if}
{:else}
	<div
		class="h-6 px-2 text-xs cursor-pointer hover:bg-gray-100 rounded transition-colors flex items-center {className}"
		onclick={startEdit}
		role="button"
		tabindex="0"
		onkeydown={(e) => e.key === 'Enter' && startEdit()}
	>
		{formatDisplayValue(value)}
	</div>
{/if}
