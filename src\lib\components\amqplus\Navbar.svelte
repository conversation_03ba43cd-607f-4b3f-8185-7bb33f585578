<script>
	import { page } from '$app/stores';
	import { Home, FileText, MessageSquare } from 'lucide-svelte';

	let mobileMenuOpen = $state(false);

	function toggleMobileMenu() {
		mobileMenuOpen = !mobileMenuOpen;
	}

	function closeMobileMenu() {
		mobileMenuOpen = false;
	}

	const currentPath = $derived($page.url.pathname);
</script>

<nav class="relative z-50 bg-white/80 backdrop-blur-md border-b border-gray-200/50 sticky top-0">
	<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
		<div class="flex justify-between items-center h-16">
			<!-- Logo/Brand -->
			<div class="flex-shrink-0">
				<a href="/" class="flex items-center space-x-2" onclick={closeMobileMenu}>
					<div class="w-8 h-8 bg-gradient-to-br from-rose-500 to-pink-600 rounded-lg flex items-center justify-center">
						<span class="text-white font-bold text-sm">A+</span>
					</div>
					<span class="font-bold text-xl text-gray-900">AMQ PLUS</span>
				</a>
			</div>

			<!-- Desktop Navigation -->
			<div class="hidden md:block">
				<div class="ml-10 flex items-baseline space-x-8">
					<a
						href="/"
						class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 {currentPath === '/' 
							? 'text-rose-600 bg-rose-50' 
							: 'text-gray-700 hover:text-rose-600 hover:bg-rose-50'}"
					>
						<Home size={16} />
						<span>Home</span>
					</a>
					<a
						href="/changelog"
						class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 {currentPath === '/changelog' 
							? 'text-rose-600 bg-rose-50' 
							: 'text-gray-700 hover:text-rose-600 hover:bg-rose-50'}"
					>
						<FileText size={16} />
						<span>Changelog</span>
					</a>
					<a
						href="/feedback"
						class="flex items-center space-x-2 px-3 py-2 rounded-md text-sm font-medium transition-colors duration-200 {currentPath === '/feedback' 
							? 'text-rose-600 bg-rose-50' 
							: 'text-gray-700 hover:text-rose-600 hover:bg-rose-50'}"
					>
						<MessageSquare size={16} />
						<span>Feedback</span>
					</a>
				</div>
			</div>

			<!-- Mobile menu button -->
			<div class="md:hidden">
				<button
					onclick={toggleMobileMenu}
					class="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-rose-600 hover:bg-rose-50 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-rose-500 transition-colors duration-200"
					aria-expanded="false"
				>
					<span class="sr-only">Open main menu</span>
					{#if !mobileMenuOpen}
						<svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
						</svg>
					{:else}
						<svg class="block h-6 w-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
						</svg>
					{/if}
				</button>
			</div>
		</div>
	</div>

	<!-- Mobile menu -->
	{#if mobileMenuOpen}
		<div class="md:hidden">
			<div class="px-2 pt-2 pb-3 space-y-1 sm:px-3 bg-white/95 backdrop-blur-md border-t border-gray-200/50">
				<a
					href="/"
					onclick={closeMobileMenu}
					class="flex items-center space-x-3 px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 {currentPath === '/'
						? 'text-rose-600 bg-rose-50'
						: 'text-gray-700 hover:text-rose-600 hover:bg-rose-50'}"
				>
					<Home size={18} />
					<span>Home</span>
				</a>
				<a
					href="/changelog"
					onclick={closeMobileMenu}
					class="flex items-center space-x-3 px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 {currentPath === '/changelog'
						? 'text-rose-600 bg-rose-50'
						: 'text-gray-700 hover:text-rose-600 hover:bg-rose-50'}"
				>
					<FileText size={18} />
					<span>Changelog</span>
				</a>
				<a
					href="/feedback"
					onclick={closeMobileMenu}
					class="flex items-center space-x-3 px-3 py-2 rounded-md text-base font-medium transition-colors duration-200 {currentPath === '/feedback'
						? 'text-rose-600 bg-rose-50'
						: 'text-gray-700 hover:text-rose-600 hover:bg-rose-50'}"
				>
					<MessageSquare size={18} />
					<span>Feedback</span>
				</a>
			</div>
		</div>
	{/if}
</nav>
