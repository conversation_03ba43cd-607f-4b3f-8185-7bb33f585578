<script>
	import { Button } from '$lib/components/ui/button';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';

	let { isOpen = $bindable(), onAddNode, existingZones = [] } = $props();

	// Centralized zone definitions - should match the ones in the main editor
	const zoneCategories = {
		mode: {
			title: 'Mode Settings Zone',
			color: '#75B9DF',
			icon: '🎮',
			description: 'Add a complete mode settings zone with scoring and answering',
			nodes: [
				{ id: 'scoring', title: 'Scoring', icon: '🏆' },
				{ id: 'answering', title: 'Answering', icon: '⌨️' }
			]
		},
		general: {
			title: 'General Settings Zone',
			color: '#DF6975',
			icon: '⚙️',
			description: 'Add a complete general settings zone with players, songs, teams, and selection options',
			nodes: [
				{ id: 'players', title: 'Players', icon: '👥' },
				{ id: 'team-size', title: 'Team Size', icon: '👫' },
				{ id: 'songs', title: 'Songs', icon: '🎵' },
				{ id: 'song-selection', title: 'Song Selection', icon: '🎲' },
				{ id: 'watched-distribution', title: 'Watched Distribution', icon: '📊' },
				{ id: 'song-types', title: 'Song Types', icon: '🎼' },
				{ id: 'openings-categories', title: 'Openings Categories', icon: '🎬' },
				{ id: 'endings-categories', title: 'Endings Categories', icon: '🎭' },
				{ id: 'inserts-categories', title: 'Inserts Categories', icon: '🎪' }
			]
		},
		quiz: {
			title: 'Quiz Settings Zone',
			color: '#75DF8B',
			icon: '⏱️',
			description: 'Add a complete quiz settings zone with timing, difficulty, and audio options',
			nodes: [
				{ id: 'guess-time', title: 'Guess Time', icon: '⏰' },
				{ id: 'extra-time', title: 'Extra Time', icon: '⏱️' },
				{ id: 'sample-point', title: 'Sample Point', icon: '🎯' },
				{ id: 'playback-speed', title: 'Playback Speed', icon: '⚡' },
				{ id: 'song-difficulty', title: 'Song Difficulty', icon: '📈' },
				{ id: 'song-popularity', title: 'Song Popularity', icon: '⭐' },
				{ id: 'modifiers', title: 'Modifiers', icon: '🔧' }
			]
		},
		anime: {
			title: 'Anime Settings Zone',
			color: '#DFB975',
			icon: '📺',
			description: 'Add a complete anime settings zone with filters, scores, and vintage options',
			nodes: [
				{ id: 'player-score', title: 'Player Score', icon: '⭐' },
				{ id: 'anime-score', title: 'Anime Score', icon: '📊' },
				{ id: 'vintage', title: 'Vintage', icon: '📅' },
				{ id: 'anime-type', title: 'Anime Type', icon: '🎬' },
				{ id: 'genres', title: 'Genres', icon: '🏷️' },
				{ id: 'tags', title: 'Tags', icon: '🔖' }
			]
		}
	};

	function handleAddZone(zoneKey) {
		// Check if zone is already added
		if (existingZones.includes(zoneKey)) {
			return; // Don't add if already exists
		}

		const zone = zoneCategories[zoneKey];
		onAddNode?.(zoneKey, zone);
	}

	// Check if a zone is available to be added
	function isZoneAvailable(zoneKey) {
		return !existingZones.includes(zoneKey);
	}
</script>

<!-- Sidebar Overlay -->
{#if isOpen}
	<div
		class="fixed inset-0 bg-black/20 backdrop-blur-sm z-40 transition-opacity duration-300"
		onclick={() => isOpen = false}
		onkeydown={(e) => e.key === 'Escape' && (isOpen = false)}
		role="button"
		tabindex="0"
		aria-label="Close sidebar"
	></div>
{/if}

<!-- Sidebar Drawer -->
<div class="fixed top-0 right-0 h-full z-50 transition-transform duration-300 ease-in-out {isOpen ? 'translate-x-0' : 'translate-x-full'}">
	<div class="h-full w-96 bg-white border-l border-gray-200 shadow-2xl flex flex-col">
		<!-- Header with top margin to avoid arrow -->
		<div class="pt-12 p-6 border-b border-gray-200 bg-gray-50">
			<div class="flex items-center justify-between mb-2">
				<h2 class="text-xl font-bold text-gray-800">Add Zone Areas</h2>
				<button
					onclick={() => isOpen = false}
					class="p-2 hover:bg-gray-200 rounded-lg transition-colors"
					aria-label="Close sidebar"
				>
					<svg class="w-5 h-5 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
					</svg>
				</button>
			</div>
			<p class="text-sm text-gray-600">
				Click on a zone to add it with all its individual setting nodes
			</p>
		</div>

		<!-- Content -->
		<div class="flex-1 overflow-y-auto p-4 space-y-4">
			{#each Object.entries(zoneCategories) as [zoneKey, zone]}
				{@const available = isZoneAvailable(zoneKey)}
				<Card class="{available ? 'cursor-pointer hover:shadow-lg hover:scale-[1.02]' : 'cursor-not-allowed opacity-50'} transition-all duration-200 border-l-4"
					  style="border-left-color: {zone.color};"
					  onclick={() => available && handleAddZone(zoneKey)}>
					<CardContent class="p-4">
						<div class="flex items-start gap-3 mb-3">
							<span class="text-2xl flex-shrink-0 {available ? '' : 'grayscale'}">{zone.icon}</span>
							<div class="flex-1 min-w-0">
								<div class="font-semibold {available ? 'text-gray-800' : 'text-gray-400'} text-base mb-1">
									{zone.title}
									{#if !available}
										<span class="text-xs font-normal text-gray-400 ml-2">(Already added)</span>
									{/if}
								</div>
								<div class="text-sm {available ? 'text-gray-600' : 'text-gray-400'} leading-relaxed mb-3">{zone.description}</div>
							</div>
						</div>

						<!-- Preview of included nodes -->
						<div class="border-t border-gray-100 pt-3">
							<div class="text-xs font-medium {available ? 'text-gray-500' : 'text-gray-400'} mb-2">Includes {zone.nodes.length} setting nodes:</div>
							<div class="flex flex-wrap gap-1">
								{#each zone.nodes as node}
									<span class="inline-flex items-center gap-1 px-2 py-1 {available ? 'bg-gray-100 text-gray-700' : 'bg-gray-50 text-gray-400'} rounded-full text-xs">
										<span class="{available ? '' : 'grayscale'}">{node.icon}</span>
										<span>{node.title}</span>
									</span>
								{/each}
							</div>
						</div>
					</CardContent>
				</Card>
			{/each}
		</div>

		<!-- Footer -->
		<div class="p-4 border-t border-gray-200 bg-gray-50">
			<div class="text-xs text-gray-500 text-center">
				Click on a zone to add it with all its setting nodes to the canvas
			</div>
		</div>
	</div>
</div>

<style>
	/* Custom scrollbar for the sidebar */
	.overflow-y-auto::-webkit-scrollbar {
		width: 6px;
	}
	
	.overflow-y-auto::-webkit-scrollbar-track {
		background: #f1f5f9;
		border-radius: 3px;
	}
	
	.overflow-y-auto::-webkit-scrollbar-thumb {
		background: #cbd5e1;
		border-radius: 3px;
	}
	
	.overflow-y-auto::-webkit-scrollbar-thumb:hover {
		background: #94a3b8;
	}
</style>
