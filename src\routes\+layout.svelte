<script>
	import "../app.css"; // Import global styles, including Tailwind
	import "$lib/styles/amqplus.css"; // Import AMQ PLUS specific styles
	import Navbar from "$lib/components/amqplus/Navbar.svelte";
	import Footer from '$lib/components/amqplus/Footer.svelte';
</script>

<div class="relative min-h-screen overflow-hidden bg-gradient-to-br from-rose-50 to-pink-50">
	<!-- Light mode gradient blobs with AMQ PLUS colors -->
	<div
		class="absolute top-[-10%] left-[-10%] w-[250px] h-[250px] xs:w-[300px] xs:h-[300px] sm:w-[400px] sm:h-[400px] md:w-[450px] md:h-[450px] lg:w-[600px] lg:h-[600px] bg-rose-300/30 rounded-full filter blur-3xl animate-slow-pulse"
	></div>
	<div
		class="absolute bottom-[-10%] right-[-10%] w-[200px] h-[200px] xs:w-[250px] xs:h-[250px] sm:w-[350px] sm:h-[350px] md:w-[400px] md:h-[400px] lg:w-[500px] lg:h-[500px] bg-pink-400/25 rounded-full filter blur-3xl animate-slow-pulse-2 animation-delay-2000"
	></div>
	<div
		class="absolute top-[20%] right-[5%] w-[150px] h-[150px] xs:w-[180px] xs:h-[180px] sm:w-[250px] sm:h-[250px] md:w-[280px] md:h-[280px] lg:w-[300px] lg:h-[300px] bg-orange-300/20 rounded-full filter blur-2xl animate-slow-pulse-3 animation-delay-4000"
	></div>

	<div class="relative z-10">
		<Navbar />
		<slot />
		<Footer />
	</div>
</div>

<style>
	/* Override the dark theme for AMQ PLUS pages */
	:global(body) {
		background-color: white;
		color: rgb(17 24 39); /* gray-900 */
	}
</style>
