<script>
 import * as Select from "$lib/components/ui/select/index.js";

 const fruits = [
  { value: "apple", label: "Apple" },
  { value: "banana", label: "Banana" },
  { value: "blueberry", label: "Blueberry" },
  { value: "grapes", label: "Grapes" },
  { value: "pineapple", label: "Pineapple" }
 ];

 let value = $state("");

 const triggerContent = $derived(
  fruits.find((f) => f.value === value)?.label ?? "Select a fruit"
 );
</script>

<div class="min-h-screen p-8 amq-theme">
 <h1 class="mb-4 text-2xl font-bold">Select Component Test</h1>

 <Select.Root type="single" name="favoriteFruit" bind:value>
  <Select.Trigger class="w-[180px]">
   {triggerContent}
  </Select.Trigger>
  <Select.Content>
   <Select.Group>
    <Select.Label>Fruits</Select.Label>
    {#each fruits as fruit (fruit.value)}
     <Select.Item
      value={fruit.value}
      label={fruit.label}
      disabled={fruit.value === "grapes"}
     >
      {fruit.label}
     </Select.Item>
    {/each}
   </Select.Group>
  </Select.Content>
 </Select.Root>
</div>