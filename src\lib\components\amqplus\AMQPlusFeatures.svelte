<script>
	import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	
	const features = [
		{
			title: 'Node-Based Editor',
			description: 'Visually design your lobby settings with an intuitive drag-and-drop interface.',
			icon: '🔗',
			badge: 'Visual',
			color: 'bg-amq-accent/10 text-amq-accent border-amq-accent/20'
		},
		{
			title: 'Advanced Filters',
			description: 'Create complex song filters that go beyond AMQ\'s standard options.',
			icon: '🎵',
			badge: 'Powerful',
			color: 'bg-amq-primary/10 text-amq-primary border-amq-primary/20'
		},
		{
			title: 'Custom Rules',
			description: 'Define unique game rules and scoring systems for your lobbies.',
			icon: '⚙️',
			badge: 'Flexible',
			color: 'bg-amq-secondary/10 text-amq-secondary border-amq-secondary/20'
		},
		{
			title: 'Export & Share',
			description: 'Save your configurations and share them with the AMQ community.',
			icon: '📤',
			badge: 'Social',
			color: 'bg-amq-success/10 text-amq-success border-amq-success/20'
		},
		{
			title: 'Template Library',
			description: 'Browse and use pre-made templates from other players.',
			icon: '📚',
			badge: 'Community',
			color: 'bg-amq-warning/10 text-amq-warning border-amq-warning/20'
		},
		{
			title: 'Real-time Preview',
			description: 'See how your settings will work before applying them to your lobby.',
			icon: '👁️',
			badge: 'Live',
			color: 'bg-amq-neutral/10 text-amq-neutral border-amq-neutral/20'
		}
	];
</script>

<section class="w-full max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
	<div class="text-center mb-16 relative z-10">
		<h2 class="text-3xl xs:text-4xl sm:text-4xl font-bold text-gray-800 mb-6">
			Powerful Features for <span class="amq-gradient-text">AMQ enjoyers</span>
		</h2>
		<p class="text-lg sm:text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
			Take your Anime Music Quiz experience to the next level with advanced customization tools
		</p>
	</div>

	<div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8 relative z-10">
		{#each features as feature}
			<Card class="bg-white/95 backdrop-blur-sm border border-amq-light rounded-xl shadow-lg hover:shadow-2xl p-6 sm:p-4 hover:border-amq-secondary group transform hover:scale-105 transition-all duration-300">
				<CardHeader class="pb-4">
					<div class="flex items-center justify-between mb-3">
						<div class="text-3xl">{feature.icon}</div>
						<Badge variant="outline" class={feature.color}>
							{feature.badge}
						</Badge>
					</div>
					<CardTitle class="text-xl sm:text-2xl text-gray-800 group-hover:text-amq-primary transition-colors duration-300">
						{feature.title}
					</CardTitle>
				</CardHeader>
				<CardContent>
					<CardDescription class="text-gray-600 leading-relaxed text-base">
						{feature.description}
					</CardDescription>
				</CardContent>
			</Card>
		{/each}
	</div>
</section>

<style>
	@import '$lib/styles/amqplus.css';
</style>
