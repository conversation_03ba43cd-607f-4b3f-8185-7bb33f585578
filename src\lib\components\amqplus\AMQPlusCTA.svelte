<script>
	import { <PERSON><PERSON> } from '$lib/components/ui/button';
	import { Card, CardContent } from '$lib/components/ui/card';
	import { Badge } from '$lib/components/ui/badge';
	import BorderBeam from '$lib/components/magic-ui/BorderBeam.svelte';

	function scrollToTemplates() {
		const templatesSection = document.getElementById('quick-start-templates');
		if (templatesSection) {
			templatesSection.scrollIntoView({
				behavior: 'smooth',
				block: 'start'
			});
		}
	}
</script>

<section class="mx-auto w-full max-w-5xl px-4 sm:px-6 lg:px-8">
	<Card
		class="border-amq-light relative rounded-xl border bg-white/90 p-6 text-center shadow-lg backdrop-blur-sm transition-all duration-300 hover:shadow-xl sm:p-8 md:p-12 lg:p-16"
	>
		<BorderBeam />
		<CardContent class="space-y-8">
			<div class="space-y-6">
				<h2 class="xs:text-4xl text-3xl leading-tight font-bold text-gray-800">
					Start Creating <span class="amq-gradient-text">Advanced</span> Lobby Settings
				</h2>

				<p class="mx-auto max-w-3xl text-lg leading-relaxed text-gray-600 sm:text-xl">
					Design custom AMQ lobby configurations with our intuitive node-based editor. Create
					settings that go beyond the game's built-in limitations.
				</p>
			</div>

			<div class="flex flex-col items-center justify-center gap-4 sm:flex-row sm:gap-6">
				<a href="/editor">
					<Button
						class="bg-amq-primary hover:bg-amq-dark min-w-[200px] cursor-pointer rounded-lg px-8 py-3 text-lg font-semibold text-white shadow-lg transition-colors duration-200 hover:shadow-xl sm:min-w-[220px] sm:px-10 sm:py-4 sm:text-xl"
					>
						Launch Editor
					</Button>
				</a>

				<Button
					variant="outline"
					onclick={scrollToTemplates}
					class="border-amq-primary text-amq-primary hover:bg-amq-primary min-w-[200px] cursor-pointer rounded-lg border-2 px-8 py-3 text-lg font-semibold transition-colors duration-200 hover:text-white sm:min-w-[220px] sm:px-10 sm:py-4 sm:text-xl"
				>
					View Examples
				</Button>
			</div>

			<div
				class="flex flex-wrap justify-center gap-4 pt-6 text-sm text-gray-500 sm:gap-6 sm:text-base"
			>
				<div class="flex items-center gap-2">
					<span class="bg-amq-success h-2 w-2 animate-pulse rounded-full"></span>
					<span>Free to use</span>
				</div>
				<div class="flex items-center gap-2">
					<span
						class="bg-amq-accent h-2 w-2 animate-pulse rounded-full"
						style="animation-delay: 0.2s;"
					></span>
					<span>No registration required</span>
				</div>
				<div class="flex items-center gap-2">
					<span
						class="bg-amq-primary h-2 w-2 animate-pulse rounded-full"
						style="animation-delay: 0.4s;"
					></span>
					<span>Community driven</span>
				</div>
			</div>
		</CardContent>
	</Card>
</section>

<style>
	@import '$lib/styles/amqplus.css';
</style>
