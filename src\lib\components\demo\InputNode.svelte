<script>
	import { <PERSON><PERSON>, Position } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Input } from '$lib/components/ui/input';
	import { Label } from '$lib/components/ui/label';

	let { data } = $props();
	let inputValue = $state(data.value || '');

	function handleInputChange(event) {
		inputValue = event.target.value;
		data.value = inputValue;
	}
</script>

<div class="input-node min-w-64">
	<Handle type="target" position={Position.Left} />
	
	<Card class="border-2 border-blue-300 bg-blue-50/50">
		<CardHeader class="pb-2">
			<CardTitle class="flex items-center gap-2 text-sm font-semibold text-blue-800">
				<span class="text-lg">📝</span>
				{data.label || 'Text Input'}
			</CardTitle>
		</CardHeader>
		<CardContent class="pt-0">
			<div class="space-y-2">
				<Label for="input-field" class="text-xs text-blue-700">Enter text:</Label>
				<Input
					id="input-field"
					type="text"
					placeholder={data.placeholder || 'Type here...'}
					value={inputValue}
					oninput={handleInputChange}
					class="text-sm"
				/>
				{#if inputValue}
					<div class="text-xs text-blue-600 bg-blue-100 p-1 rounded">
						Output: {inputValue}
					</div>
				{/if}
			</div>
		</CardContent>
	</Card>
	
	<Handle type="source" position={Position.Right} />
</div>

<style>
	.input-node {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}
</style>
