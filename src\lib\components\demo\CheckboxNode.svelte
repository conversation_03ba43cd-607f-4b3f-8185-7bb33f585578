<script>
	import { <PERSON><PERSON>, Position } from '@xyflow/svelte';
	import { Card, CardContent, CardHeader, CardTitle } from '$lib/components/ui/card';
	import { Checkbox } from '$lib/components/ui/checkbox';
	import { Label } from '$lib/components/ui/label';

	let { data } = $props();
	let checkboxValues = $state(data.values || {});

	const options = data.options || [
		{ id: 'option1', label: 'Option 1' },
		{ id: 'option2', label: 'Option 2' },
		{ id: 'option3', label: 'Option 3' }
	];

	function handleCheckboxChange(optionId, checked) {
		checkboxValues = { ...checkboxValues, [optionId]: checked };
		data.values = checkboxValues;
	}

	// Initialize checkbox values if not set
	$effect(() => {
		if (!checkboxValues || Object.keys(checkboxValues).length === 0) {
			const initialValues = {};
			options.forEach(option => {
				initialValues[option.id] = checkboxValues?.[option.id] || false;
			});
			checkboxValues = initialValues;
			data.values = initialValues;
		}
	});

	const selectedOptions = $derived(Object.entries(checkboxValues)
		.filter(([_, checked]) => checked)
		.map(([id, _]) => options.find(opt => opt.id === id)?.label)
		.filter(Boolean));
</script>

<div class="checkbox-node min-w-64">
	<Handle type="target" position={Position.Left} />
	
	<Card class="border-2 border-orange-300 bg-orange-50/50">
		<CardHeader class="pb-2">
			<CardTitle class="flex items-center gap-2 text-sm font-semibold text-orange-800">
				<span class="text-lg">☑️</span>
				{data.label || 'Multiple Choice'}
			</CardTitle>
		</CardHeader>
		<CardContent class="pt-0">
			<div class="space-y-3">
				<div class="space-y-2">
					{#each options as option}
						<div class="flex items-center space-x-2">
							<Checkbox
								checked={checkboxValues[option.id] || false}
								onCheckedChange={(checked) => handleCheckboxChange(option.id, checked)}
								id={`checkbox-${option.id}`}
							/>
							<Label for={`checkbox-${option.id}`} class="text-sm text-orange-700">
								{option.label}
							</Label>
						</div>
					{/each}
				</div>
				{#if selectedOptions.length > 0}
					<div class="p-2 text-xs text-orange-600 bg-orange-100 rounded">
						Selected: {selectedOptions.join(', ')}
					</div>
				{/if}
			</div>
		</CardContent>
	</Card>
	
	<Handle type="source" position={Position.Right} />
</div>

<style>
	.checkbox-node {
		font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
	}
</style>
