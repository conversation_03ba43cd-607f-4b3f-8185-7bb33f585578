const svgToDataUri = require("mini-svg-data-uri");

// Fallback function for flattenColorPalette
function flattenColorPalette(colors) {
  const result = {};

  function flatten(obj, prefix = '') {
    for (const [key, value] of Object.entries(obj)) {
      const newKey = prefix ? `${prefix}-${key}` : key;

      if (typeof value === 'string') {
        result[newKey] = value;
      } else if (typeof value === 'object' && value !== null) {
        flatten(value, newKey);
      }
    }
  }

  flatten(colors);
  return result;
}

/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [],
  theme: {
    extend: {
      colors: {
        // AMQ PLUS color scheme based on #DF6975
        amq: {
          primary: '#DF6975',      // Main brand color
          secondary: '#E8899A',    // Lighter variant
          accent: '#75B9DF',       // Complementary blue
          light: '#F5E6E8',        // Very light pink
          dark: '#B54A5A',         // Darker variant
          neutral: '#8B7B7A',      // Neutral brown-gray
          success: '#75DF8B',      // Success green
          warning: '#DFB975',      // Warning orange
        }
      },

      animation: {
        "border-beam": "border-beam calc(var(--duration)*1s) infinite linear",

        "gradient-x": "gradient-x 3s ease infinite",
        "gradient-x-reverse": "gradient-x-reverse 3s ease infinite",

      },
      keyframes: {
        "border-beam": {
          "100%": {
            "offset-distance": "100%",
          },
        },

        "gradient-x": {
          "0%, 100%": {
            "background-size": "200% 200%",
            "background-position": "left center"
          },
          "50%": {
            "background-size": "200% 200%",
            "background-position": "right center"
          }
        },
        "gradient-x-reverse": {
          "0%, 100%": {
            "background-size": "200% 200%",
            "background-position": "right center"
          },
          "50%": {
            "background-size": "200% 200%",
            "background-position": "left center"
          }
        },

      },
    },
  },
  plugins: [
    addVariablesForColors,
    function ({ matchUtilities, theme }) {
      matchUtilities(
        {
          "bg-grid": (value) => ({
            backgroundImage: `url("${svgToDataUri(
              `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="32" height="32" fill="none" stroke="${value}"><path d="M0 .5H31.5V32"/></svg>`
            )}")`,
          }),
          "bg-grid-small": (value) => ({
            backgroundImage: `url("${svgToDataUri(
              `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="8" height="8" fill="none" stroke="${value}"><path d="M0 .5H31.5V32"/></svg>`
            )}")`,
          }),
          "bg-dot": (value) => ({
            backgroundImage: `url("${svgToDataUri(
              `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 32 32" width="16" height="16" fill="none"><circle fill="${value}" id="pattern-circle" cx="10" cy="10" r="1.6257413380501518"></circle></svg>`
            )}")`,
          }),
        },
        { values: flattenColorPalette(theme("backgroundColor")), type: "color" }
      );
    },
  ],
};

// This plugin adds each Tailwind color as a global CSS variable, e.g. var(--gray-200).
function addVariablesForColors({ addBase, theme }) {
  let allColors = flattenColorPalette(theme("colors"));
  let newVars = Object.fromEntries(
    Object.entries(allColors).map(([key, val]) => [`--${key}`, val])
  );

  addBase({
    ":root": newVars,
  });
}
