<script>
	let { settings } = $props();

	const watchedDistributionOptions = [
		{ value: 'random', label: 'Random' },
		{ value: 'weighted', label: 'Weighted' },
		{ value: 'equal', label: 'Equal' }
	];

	// Handle song selection percentages
	const updateSongSelection = (type, value) => {
		const numValue = parseInt(value) || 0;
		const total = settings.songSelection.random + settings.songSelection.mix + settings.songSelection.watched;
		const others = total - settings.songSelection[type];
		
		if (numValue <= 100 && others + numValue <= 100) {
			settings.songSelection[type] = numValue;
		}
	};

	// Handle song types percentages
	const updateSongTypes = (type, value) => {
		const numValue = parseInt(value) || 0;
		const total = settings.songTypes.openings + settings.songTypes.endings + settings.songTypes.inserts;
		const others = total - settings.songTypes[type];
		
		if (numValue <= 100 && others + numValue <= 100) {
			settings.songTypes[type] = numValue;
		}
	};
</script>

<div class="space-y-4">
	<!-- Number of Players -->
	<div class="space-y-2">
		<label for="numberOfPlayers" class="text-sm font-medium text-gray-700">Number of Players</label>
		<input
			id="numberOfPlayers"
			type="number"
			bind:value={settings.numberOfPlayers}
			min="1"
			max="50"
			class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
		/>
	</div>

	<!-- Team Size -->
	<div class="space-y-2">
		<label for="teamSize" class="text-sm font-medium text-gray-700">Team Size</label>
		<input
			id="teamSize"
			type="number"
			bind:value={settings.teamSize}
			min="1"
			max="10"
			class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
		/>
	</div>

	<!-- Number of Songs -->
	<div class="space-y-2">
		<label for="numberOfSongs" class="text-sm font-medium text-gray-700">Number of Songs</label>
		<input
			id="numberOfSongs"
			type="number"
			bind:value={settings.numberOfSongs}
			min="1"
			max="100"
			class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
		/>
	</div>

	<!-- Song Selection -->
	<div class="space-y-2">
		<div class="text-sm font-medium text-gray-700">Song Selection (%)</div>
		<div class="grid grid-cols-3 gap-2">
			<div>
				<label for="songSelectionRandom" class="text-xs text-gray-600">Random</label>
				<input
					id="songSelectionRandom"
					type="number"
					value={settings.songSelection.random}
					oninput={(e) => updateSongSelection('random', e.target.value)}
					min="0"
					max="100"
					class="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
				/>
			</div>
			<div>
				<label for="songSelectionMix" class="text-xs text-gray-600">Mix</label>
				<input
					id="songSelectionMix"
					type="number"
					value={settings.songSelection.mix}
					oninput={(e) => updateSongSelection('mix', e.target.value)}
					min="0"
					max="100"
					class="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
				/>
			</div>
			<div>
				<label for="songSelectionWatched" class="text-xs text-gray-600">Watched</label>
				<input
					id="songSelectionWatched"
					type="number"
					value={settings.songSelection.watched}
					oninput={(e) => updateSongSelection('watched', e.target.value)}
					min="0"
					max="100"
					class="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
				/>
			</div>
		</div>
	</div>

	<!-- Watched Distribution -->
	<div class="space-y-2">
		<label for="watchedDistribution" class="text-sm font-medium text-gray-700">Watched Distribution</label>
		<select
			id="watchedDistribution"
			bind:value={settings.watchedDistribution}
			class="w-full px-3 py-2 text-sm border border-gray-300 rounded-md bg-white focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
		>
			{#each watchedDistributionOptions as option}
				<option value={option.value}>{option.label}</option>
			{/each}
		</select>
	</div>

	<!-- Song Types -->
	<div class="space-y-2">
		<div class="text-sm font-medium text-gray-700">Song Types (%)</div>
		<div class="grid grid-cols-3 gap-2">
			<div>
				<label for="songTypesOpenings" class="text-xs text-gray-600">Openings</label>
				<input
					id="songTypesOpenings"
					type="number"
					value={settings.songTypes.openings}
					oninput={(e) => updateSongTypes('openings', e.target.value)}
					min="0"
					max="100"
					class="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
				/>
			</div>
			<div>
				<label for="songTypesEndings" class="text-xs text-gray-600">Endings</label>
				<input
					id="songTypesEndings"
					type="number"
					value={settings.songTypes.endings}
					oninput={(e) => updateSongTypes('endings', e.target.value)}
					min="0"
					max="100"
					class="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
				/>
			</div>
			<div>
				<label for="songTypesInserts" class="text-xs text-gray-600">Inserts</label>
				<input
					id="songTypesInserts"
					type="number"
					value={settings.songTypes.inserts}
					oninput={(e) => updateSongTypes('inserts', e.target.value)}
					min="0"
					max="100"
					class="w-full px-2 py-1 text-sm border border-gray-300 rounded bg-white focus:outline-none focus:ring-1 focus:ring-blue-500"
				/>
			</div>
		</div>
	</div>

	<!-- Song Categories -->
	<div class="space-y-3">
		<div class="text-sm font-medium text-gray-700">Song Categories</div>

		<!-- Openings -->
		<div class="space-y-2">
			<div class="text-xs font-medium text-gray-600">Openings</div>
			<div class="grid grid-cols-2 gap-2">
				<label class="flex items-center space-x-2">
					<input type="checkbox" bind:checked={settings.songCategories.openings.standard} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
					<span class="text-xs">Standard</span>
				</label>
				<label class="flex items-center space-x-2">
					<input type="checkbox" bind:checked={settings.songCategories.openings.instrumental} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
					<span class="text-xs">Instrumental</span>
				</label>
				<label class="flex items-center space-x-2">
					<input type="checkbox" bind:checked={settings.songCategories.openings.chanting} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
					<span class="text-xs">Chanting</span>
				</label>
				<label class="flex items-center space-x-2">
					<input type="checkbox" bind:checked={settings.songCategories.openings.character} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
					<span class="text-xs">Character</span>
				</label>
			</div>
		</div>

		<!-- Endings -->
		<div class="space-y-2">
			<div class="text-xs font-medium text-gray-600">Endings</div>
			<div class="grid grid-cols-2 gap-2">
				<label class="flex items-center space-x-2">
					<input type="checkbox" bind:checked={settings.songCategories.endings.standard} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
					<span class="text-xs">Standard</span>
				</label>
				<label class="flex items-center space-x-2">
					<input type="checkbox" bind:checked={settings.songCategories.endings.instrumental} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
					<span class="text-xs">Instrumental</span>
				</label>
				<label class="flex items-center space-x-2">
					<input type="checkbox" bind:checked={settings.songCategories.endings.chanting} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
					<span class="text-xs">Chanting</span>
				</label>
				<label class="flex items-center space-x-2">
					<input type="checkbox" bind:checked={settings.songCategories.endings.character} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
					<span class="text-xs">Character</span>
				</label>
			</div>
		</div>

		<!-- Inserts -->
		<div class="space-y-2">
			<div class="text-xs font-medium text-gray-600">Inserts</div>
			<div class="grid grid-cols-2 gap-2">
				<label class="flex items-center space-x-2">
					<input type="checkbox" bind:checked={settings.songCategories.inserts.standard} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
					<span class="text-xs">Standard</span>
				</label>
				<label class="flex items-center space-x-2">
					<input type="checkbox" bind:checked={settings.songCategories.inserts.instrumental} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
					<span class="text-xs">Instrumental</span>
				</label>
				<label class="flex items-center space-x-2">
					<input type="checkbox" bind:checked={settings.songCategories.inserts.chanting} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
					<span class="text-xs">Chanting</span>
				</label>
				<label class="flex items-center space-x-2">
					<input type="checkbox" bind:checked={settings.songCategories.inserts.character} class="rounded border-gray-300 text-blue-600 focus:ring-blue-500" />
					<span class="text-xs">Character</span>
				</label>
			</div>
		</div>
	</div>
</div>
